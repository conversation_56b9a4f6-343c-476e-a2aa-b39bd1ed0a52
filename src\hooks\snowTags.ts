"use client";

import { useParams } from "next/navigation";
import toast from "react-hot-toast";
import type {
  SnowFormParentStates,
  SnowFormProps,
} from "~/components/snow/SnowFormContainer";
import { SnowEvent, SnowFeatureType, SnowLandingType } from "~/lib/enums/snow";
import { getStringBoolean, getStringNumber } from "~/lib/utils";
import { api } from "~/trpc/react";
import { useStore } from "./store";

export const useSnowTagsSetData = () => {
  const utils = api.useUtils();
  const selectedAthlete = useStore((state) => state.selectedAthlete);

  const updateRunScoreInState = ({
    raceId,
    selectedRunId,
    runScore,
    overallScore,
  }: {
    raceId: string;
    selectedRunId?: string;
    runScore: number;
    overallScore: number;
  }) => {
    if (!selectedRunId) return;
    utils.snow.getRace.setData({ raceId }, (prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        results: prev.results.map((x) => {
          if (x.athlete_id === selectedAthlete) {
            return {
              ...x,
              runs: x.runs.map((y) => {
                if (y.run_id === selectedRunId) {
                  return {
                    ...y,
                    score: runScore,
                    overall_score: overallScore,
                  };
                }
                return y;
              }),
            };
          }
          return x;
        }),
      };
    });
  };

  return { updateRunScoreInState };
};

export const useUpsertSnowTags = ({
  event,
  parentData,
  raceId,
  runId,
}: {
  event?: SnowEvent;
  parentData: SnowFormParentStates;
  raceId?: string;
  runId?: string;
}) => {
  const utils = api.useUtils();
  const params = useParams();
  const { updateRunScoreInState } = useSnowTagsSetData();
  const videoId = params.id as string;
  const { startFrame, endFrame, runScore } = parentData;

  const onSuccess = () => {
    if (!runId || !raceId) return;
    void utils.snow.getTags.invalidate({
      runId,
      event,
    });
    updateRunScoreInState({
      raceId,
      selectedRunId: runId,
      runScore: +parentData.runScore!,
      overallScore: +parentData.overallScore!,
    });
  };

  const { mutate: upsertHalfpipeTag, isPending: isPendingUpsertHalfpipeTag } =
    api.snow.upsertHalfPipeTag.useMutation({
      onError: (error) => {
        toast.error(error.message);
      },
      onSuccess: () => onSuccess(),
    });
  const { mutate: upsertBigAirTag, isPending: isPendingUpsertBigAirTag } =
    api.snow.upsertBigAirTag.useMutation({
      onError: (error) => {
        toast.error(error.message);
      },
      onSuccess: () => onSuccess(),
    });
  const {
    mutate: upsertSlopeStyleTag,
    isPending: isPendingUpsertSlopeStyleTag,
  } = api.snow.upsertSlopeStyleTag.useMutation({
    onError: (error) => {
      toast.error(error.message);
    },
    onSuccess: () => onSuccess(),
  });

  const onUpsertHalfpipeTag = ({
    formValues,
    resultId,
    run,
  }: {
    formValues: SnowFormProps;
    resultId: string;
    run: number;
  }) => {
    if (!runId) return;
    const { jumpTypeId, trickNum } = formValues;

    const isRunScoreValid = !!runScore && !isNaN(+runScore);

    if (!jumpTypeId) {
      toast.error("Please select a jump type");
      return;
    }
    if (event === SnowEvent.HALF_PIPE && (!trickNum || isNaN(+trickNum))) {
      toast.error("Please select a hit number");
      return;
    }

    const landingDescriptions =
      formValues.landingType === SnowLandingType.none
        ? []
        : formValues.landingDescriptions;

    upsertHalfpipeTag({
      id: formValues.id,
      videoId,
      resultId,
      run,
      snowSportsRunId: runId,
      //tag data
      startFrame: +startFrame,
      endFrame: +endFrame,
      jumpTypeId: +jumpTypeId,
      jumpTakeoffModifierId: getStringNumber(formValues.jumpTakeoffModifierId),
      progression: getStringBoolean(formValues.progression),
      switch: getStringBoolean(formValues.switch),
      cab: getStringBoolean(formValues.cab),
      spinDirection: formValues.spinDirection,
      spinTypeId: getStringNumber(formValues.spinTypeId),
      spinAmount: getStringNumber(formValues.spinAmount),
      spinModifierId: getStringNumber(formValues.spinModifierId),
      grabTypeId: getStringNumber(formValues.grabTypeId),
      grabStart: getStringNumber(formValues.grabStart),
      grabEnd: getStringNumber(formValues.grabEnd),
      takeOffFrame: getStringNumber(formValues.takeOffFrame),
      landingFrame: getStringNumber(formValues.landingFrame),
      executionId: getStringNumber(formValues.executionId),
      landingZone: formValues.landingZone,
      landingType: formValues.landingType,
      landingDescriptions,
      runScore: isRunScoreValid ? +runScore : undefined, //pta
      trickNum: getStringNumber(trickNum)!,
      amplitude: getStringNumber(formValues.amplitude),
    });
  };

  const onUpsertBigAirTag = ({
    formValues,
    resultId,
    run,
  }: {
    formValues: SnowFormProps;
    resultId: string;
    run: number;
  }) => {
    if (!runId) return;
    const { jumpTypeId } = formValues;

    if (!jumpTypeId) {
      toast.error("Please select a jump type");
      return;
    }

    const landingDescriptions =
      formValues.landingType === SnowLandingType.none
        ? []
        : formValues.landingDescriptions;

    upsertBigAirTag({
      id: formValues.id,
      videoId,
      resultId,
      run,
      snowSportsRunId: runId,
      //tag data
      startFrame: +startFrame,
      endFrame: +endFrame,
      jumpTypeId: +jumpTypeId,
      jumpTakeoffModifierId: getStringNumber(formValues.jumpTakeoffModifierId),
      progression: getStringBoolean(formValues.progression),
      switch: getStringBoolean(formValues.switch),
      cab: getStringBoolean(formValues.cab),
      spinDirection: formValues.spinDirection,
      spinTypeId: getStringNumber(formValues.spinTypeId),
      spinAmount: getStringNumber(formValues.spinAmount),
      spinModifierId: getStringNumber(formValues.spinModifierId),
      grabTypeId: getStringNumber(formValues.grabTypeId),
      grabStart: getStringNumber(formValues.grabStart),
      grabEnd: getStringNumber(formValues.grabEnd),
      takeOffFrame: getStringNumber(formValues.takeOffFrame),
      landingFrame: getStringNumber(formValues.landingFrame),
      executionId: getStringNumber(formValues.executionId),
      landingZone: formValues.landingZone,
      landingType: formValues.landingType,
      landingDescriptions,
      score: getStringNumber(formValues.score ?? null),
    });
  };

  //todo: can make this send array of tags
  const onUpsertSlopeStyleTag = ({
    formValues,
    resultId,
    run,
    sectionType,
  }: {
    formValues: SnowFormProps;
    resultId: string;
    run: number;
    sectionType: SnowFeatureType;
  }) => {
    if (!runId || !raceId) return;
    const { jumpTypeId } = formValues;
    const { featureTypeId, overallScore, sectionNum } = parentData;

    if (!jumpTypeId && sectionType === SnowFeatureType.jump) {
      toast.error("Please select a jump type");
      return;
    }
    if (!featureTypeId) {
      toast.error("Please select a feature type");
      return;
    }

    const landingDescriptions =
      formValues.landingType === SnowLandingType.none
        ? []
        : formValues.landingDescriptions;

    upsertSlopeStyleTag({
      videoId,
      resultId,
      run,
      runScore: runScore ? +runScore : undefined,
      overallScore: overallScore ? +overallScore : undefined,
      snowSportsRunId: runId,
      raceId,
      tags: [
        {
          //tag data
          id: formValues.id,
          startFrame: +startFrame,
          endFrame: +endFrame,
          jumpTypeId: +(jumpTypeId ?? 0),
          jumpTakeoffModifierId: getStringNumber(
            formValues.jumpTakeoffModifierId,
          ),
          progression: getStringBoolean(formValues.progression),
          switch: getStringBoolean(formValues.switch),
          cab: getStringBoolean(formValues.cab),
          spinDirection: formValues.spinDirection,
          spinTypeId: getStringNumber(formValues.spinTypeId),
          spinAmount: getStringNumber(formValues.spinAmount),
          spinModifierId: getStringNumber(formValues.spinModifierId),
          grabTypeId: getStringNumber(formValues.grabTypeId),
          grabStart: getStringNumber(formValues.grabStart),
          grabEnd: getStringNumber(formValues.grabEnd),
          takeOffFrame: getStringNumber(formValues.takeOffFrame),
          landingFrame: getStringNumber(formValues.landingFrame),
          executionId: getStringNumber(formValues.executionId),
          landingZone: formValues.landingZone,
          landingType: formValues.landingType,
          landingDescriptions,
          score: getStringNumber(formValues.score ?? null),
          featureId: featureTypeId,
          sectionNum: +(sectionNum ?? 1), //section feature number
          sectionType,
          transitionTypeId: getStringNumber(
            formValues.transitionTypeId ?? null,
          ),
          rail: {
            railFeatureId: getStringNumber(formValues.railFeatureId ?? null),
            railInSpinDirection: formValues.railInSpinDirection ?? null,
            railInSpinAmount: getStringNumber(
              formValues.railInSpinAmount ?? null,
            ),
            railInSpinModifierId: getStringNumber(
              formValues.railInSpinModifierId ?? null,
            ),
            railInGrabId: getStringNumber(formValues.railInGrabId ?? null),
            railTrickId: getStringNumber(formValues.railTrickId ?? null),
            railOnSpinDirection: formValues.railOnSpinDirection,
            railOnSpinAmount: getStringNumber(
              formValues.railOnSpinAmount ?? null,
            ),
            railOnGrabId: getStringNumber(formValues.railOnGrabId ?? null),
            railOnSpinModifierId: getStringNumber(
              formValues.railOnSpinModifierId ?? null,
            ),
            railOutSpinDirection: formValues.railOutSpinDirection ?? null,
            railOutSpinAmount: getStringNumber(
              formValues.railOutSpinAmount ?? null,
            ),
            railOutSpinModifierId: getStringNumber(
              formValues.railOutSpinModifierId ?? null,
            ),
            railOutGrabId: getStringNumber(formValues.railOutGrabId ?? null),
          },
        },
      ],
    });
  };

  return {
    onUpsertHalfpipeTag,
    isPendingUpsertHalfpipeTag,
    onUpsertBigAirTag,
    isPendingUpsertBigAirTag,
    onUpsertSlopeStyleTag,
    isPendingUpsertSlopeStyleTag,
  };
};
