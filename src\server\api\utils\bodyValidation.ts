import { TRPCError } from "@trpc/server";
import type { NextRequest } from "next/server";
import type { SwimmingTag } from "~/lib/enums/swimming";
import {
  type ShotputHand,
  type ShotputMovement,
  type ShotputTag,
  type ShotputType,
} from "~/lib/enums/shotput";
import type { HighJumpSide } from "~/lib/enums/highJump";

interface SwimmingBody {
  athleteId: string;
  frame: number;
  tag: SwimmingTag;
  aiConfidence?: number;
  x1?: null;
  x2?: null;
  y1?: null;
  y2?: null;
}

export const validateSwimmingBody = async (request: NextRequest) => {
  const body = (await request.json()) as SwimmingBody[];
  const firstTag = body?.[0];

  if (!firstTag?.athleteId || !firstTag.frame || !firstTag.tag) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid body",
    });
  }

  return body;
};

interface ShotputBody {
  throws: {
    athleteId: string;
    number: number;
    movement: ShotputMovement;
    type: ShotputType;
    hand: ShotputHand;
    tags: {
      phase: ShotputTag;
      frame: number;
    }[];
  }[];
}

interface HighjumpBody {
  jumps: {
    videoId: string;
    athleteId: string;
    number: number;
    startFrame: number;
    endFrame: number;
    approachSide: HighJumpSide;
    userId: string;
    strides: {
      jumpId: number;
      number: number;
      heelContact: number;
      toeOff: number;
      userId: string;
    }[];
  }[];
}

export const validateHighjumpBody = async (request: NextRequest) => {
  const body = (await request.json()) as HighjumpBody;
  const firstJump = body.jumps?.[0];
  if (
    !firstJump?.videoId ||
    !firstJump.athleteId ||
    !firstJump.number ||
    !firstJump.startFrame ||
    !firstJump.endFrame ||
    !firstJump.approachSide ||
    !firstJump.userId ||
    !firstJump.strides
  ) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid Body",
    });
  }
  return body;
};

export const validateShotputBody = async (request: NextRequest) => {
  const body = (await request.json()) as ShotputBody;
  const firstThrow = body?.throws?.[0];

  if (
    !firstThrow?.athleteId ||
    !firstThrow.number ||
    !firstThrow.movement ||
    !firstThrow.type ||
    !firstThrow.hand ||
    !firstThrow.tags
  ) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Invalid body",
    });
  }

  return body;
};
