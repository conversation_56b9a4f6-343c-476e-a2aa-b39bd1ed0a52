import type { GetPoseInfoOutput } from "~/server/api/routers/pose";

export type PartialBodyKeypoints = GetPoseInfoOutput["keypoints"];
export type PartialBodyAngles = GetPoseInfoOutput["angles"];
// Drawing modes
export type DrawingMode = "line" | "rectangle";

// Line colors
export type LineColor = "red" | "green" | "blue";

// Colored line structure
export interface ColouredLine {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  color: string;
  strokeWidth: number;
}

// Current line being drawn
export interface CurrentLine {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  strokeWidth?: number;
}

// Pose data organized by frame
export interface PoseFrameMap {
  keypoints: Map<number, PartialBodyKeypoints>;
  angles: Map<number, PartialBodyAngles>;
}

// Current frame pose data
export interface CurrentPose {
  keypoints: PartialBodyKeypoints;
  angles: PartialBodyAngles;
}

// Angle anchor points mapping
export const angleAnchors: Record<string, number> = {
  left_leg: 5, // e.g. ankle
  left_arm: 13, // e.g. left wrist
  right_leg: 2, // e.g. ankle
  right_arm: 16, // e.g. right wrist
};
