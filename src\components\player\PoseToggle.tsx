"use client";

import { Checkbox } from "~/components/ui/check-box";
import { Label } from "~/components/ui/label";

interface PoseOverlayToggleProps {
  onChange: (showPose: boolean) => void;
  defaultEnabled?: boolean;
}

export const PoseOverlayToggle = ({
  onChange,
  defaultEnabled = true,
}: PoseOverlayToggleProps) => {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox
        id="pose-overlay-toggle"
        defaultChecked={defaultEnabled}
        onCheckedChange={(checked) => onChange(checked === true)}
      />
      <Label htmlFor="pose-overlay-toggle" className="text-xs text-black/60">
        Show Pose
      </Label>
    </div>
  );
};
