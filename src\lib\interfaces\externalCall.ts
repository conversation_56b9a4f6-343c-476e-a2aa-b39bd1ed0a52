import { Gender } from "../enums/enums";
import type { SnowEvent, SnowRunSource } from "../enums/snow";
import type { SwimCourseType } from "../enums/swimming";

export interface PTACompetition {
  competition_id: string;
  start_date: string;
  location: string;
  competition_name: string;
  sport: string;
  type: string;
  swim_course_type: SwimCourseType;
  is_official: boolean;
}

export interface PTAAthlete {
  athlete_id: string;
  first_name: string;
  last_name: string;
  gender: string;
  dob: string;
  country: string;
  sport: string;
  active: boolean;
  is_hp: boolean;
}

export interface SnowRun {
  run_id: string;
  result_id: string;
  run: number;
  score: number;
  section_score: number;
  overall_score: number;
}

export interface SnowRaceResult {
  result_id: string;
  athlete_id: string;
  athlete: {
    country: string;
    dob: string;
    gender: "Women" | "Men";
    first_name: string;
    last_name: string;
    sport: string;
  };
  cup_points: number;
  fis_points: number;
  placing: number;
  runs: SnowRun[];
  score: number;
}

export interface SnowsportRace {
  event_id: string;
  competition_id: string;
  round: string;
  date: string;
  gender: Gender;
  event: SnowEvent;
  discipline: string;
  age_category: string;
  analysis_pdf: string;
  source: SnowRunSource;
  results: SnowRaceResult[];
  competition?: PTACompetition;
}
