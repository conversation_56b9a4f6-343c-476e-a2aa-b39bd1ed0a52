import { eq } from "drizzle-orm";
import { z } from "zod";
import { bodyKeypoints, bodyAngles } from "~/server/db/schema";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import type { RouterOutputs } from "~/trpc/react";

type Outputs = RouterOutputs["pose"];

export type GetPoseInfoOutput = Outputs["getAllPoseDataByVideo"];

export const poseRouter = createTRPCRouter({
  getAllPoseDataByVideo: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { videoId } = input;
      const [keypoints, angles] = await Promise.all([
        ctx.db.query.bodyKeypoints.findMany({
          columns: {
            frameNumber: true,
            keypointNum: true,
            x: true,
            y: true,
          },
          where: eq(bodyKeypoints.videoId, videoId),
        }),

        ctx.db.query.bodyAngles.findMany({
          columns: {
            frameNumber: true,
            name: true,
            angle: true,
          },
          where: eq(bodyAngles.videoId, videoId),
        }),
      ]);
      return {
        videoId,
        keypoints,
        angles,
      };
    }),
});
