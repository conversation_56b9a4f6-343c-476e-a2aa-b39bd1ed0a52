import type { ShotputThrows } from "~/server/db/schema";
import type { Sport, TagAction, UserRole, VideoStatus } from "./enums/enums";
import type { SwimCourseType } from "./enums/swimming";
import type { SnowsportRace } from "./interfaces/externalCall";

export interface Option {
  label: string;
  value: string;
  children?: Option[];
  hotkey?: {
    value: string;
    onClick: () => void;
    className?: string;
  };
}

export interface DecodedToken {
  exp: number;
  sub: string;
  typ: string;
  azp: string;
  kid: string;
  email_verified: boolean;
  aud: string[];
  roles: UserRole[];
  name: string;
  preferred_username: string;
  given_name: string;
  family_name: string;
  email: string;
}

export interface RequestTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  "not-before-policy": number;
  session_state: string;
  scope: string;
}

export interface M3u8Segment {
  duration: number;
  uri: string;
}

export interface Competition {
  id: string;
  date: string | null;
  isOfficial: boolean;
  location: string | null;
  name: string;
  sport: string;
  type: string | null;
  swimCourseType?: SwimCourseType | null;
  snowsportRace?: SnowsportRace | null;
}

export interface TagsOutput {
  tags: {
    id: string;
    throwId?: number;
    videoId: string;
    userId: string;
    isDeleted: boolean;
    frame: number;
    tag: string;
    aiTagId?: string | null;
    athleteId: string;
    x1?: number | null;
    x2?: number | null;
    y1?: number | null;
    y2?: number | null;
  }[];
  throws?: ShotputThrows[];
}

export interface Video {
  id: string;
  sport: Sport;
  title: string;
  status: VideoStatus;
  thumbnail: string;
  tags?: { text: string }[]; //this is video metadata, not tags
  athletes?: { athleteId: string; name?: string }[];
  competition?: Competition | null;
  snowSportsRaceId?: string | null;
  fps: number | null;
  createdAt: string;
}

export interface VideoPlayback {
  thumbnailUrl: string;
  videoUrl: string;
}

export interface TagChange {
  tagId: string;
  throwNumber?: number; //shotput only
  athleteId: [string, string?];
  // timestamp: number;
  action: TagAction;
  tagType: [string, string?];
  frame: [number, number?];
}

//athlete in video portal
export interface Athlete {
  athleteId: string;
  isHp: boolean;
  name: string;
}

export interface TaglistTag {
  id: string;
  throwId?: number; //shotput only
  throwNumber?: number; //shotput only
  videoId?: string;
  userId: string;
  sprintId?: string;
  frame: number;
  aiFrame?: number | null;
  raceId?: string;
  tag: string;
  athleteId: string;
  isDeleted?: boolean;
  aiTagId?: string | null;
  type?: string;
  aiConfidence?: number | null;
  x1?: number | null;
  x2?: number | null;
  y1?: number | null;
  y2?: number | null;
}

export type TagUI =
  | {
      value: string;
      key?: string;
      className?: string;
      keyword?: string;
      label?: string;
    }
  | undefined;

export type ColouredLine = {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  color: "red" | "green" | "blue";
};

export type DrawingMode = "line" | "rectangle";

/**
 * Especially include athletic sports
 */
export type AllSport = Sport | "shotput" | "highJump" | "discus" | "sprinting";

export interface Trick {
  jump_takeoff_modifiers?: string;
  jump_direction?: string;
  jump_spin_type?: string;
  jump_spin_amount?: number;
  jump_spin_modifiers?: string;
  switch: boolean;
  grab_type?: string;
  cab: boolean;
  rail_in_spin_direction?: string;
  rail_in_spin_amount?: number;
  rail_in_spin_modifiers?: string;
  rail_in_grab?: string;
  rail_trick?: string;
  rail_on_spin_direction?: string;
  rail_on_spin_amount?: number;
  rail_on_spin_modifiers?: string;
  rail_on_grab?: string;
  rail_out_spin_direction?: string;
  rail_out_spin_amount?: number;
  rail_out_spin_modifiers?: string;
  rail_out_grab?: string;
}
