"use client";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { PoseOverlayToggle } from "./PoseToggle";
import { DrawingModePicker } from "./DrawingModePicker";
import { LineColourPicker } from "./LineColourPicker";

interface DrawingToolsDropdownProps {
  setShowPose: (show: boolean) => void;
  handleUndoLine: () => void;
  handleClearLines: () => void;
  selectedColour: "red" | "green" | "blue";
  setSelectedColour: (colour: "red" | "green" | "blue") => void;
  drawingMode: "line" | "rectangle";
  setDrawingMode: (mode: "line" | "rectangle") => void;
  strokeWidth: number;
  setStrokeWidth: (w: number) => void;
}

export function PoseControlMenu({
  setShowPose,
  handleUndoLine,
  handleClearLines,
  selectedColour,
  setSelectedColour,
  drawingMode,
  setDrawingMode,
  strokeWidth,
  setStrokeWidth,
}: DrawingToolsDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Drawing Tools
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 space-y-2 p-2">
        <DropdownMenuLabel>Pose & Drawing Options</DropdownMenuLabel>
        <div className="px-1">
          <PoseOverlayToggle onChange={setShowPose} />
        </div>
        <DropdownMenuSeparator />
        <div className="flex justify-between gap-2 px-1">
          <Button
            variant="secondary"
            className="w-full"
            onClick={handleUndoLine}
          >
            Undo Line
          </Button>
          <Button
            variant="secondary"
            className="w-full"
            onClick={handleClearLines}
          >
            Clear All
          </Button>
        </div>
        <DropdownMenuSeparator />
        <div className="flex flex-col gap-2.5">
          <div className="px-1">
            <LineColourPicker
              selected={selectedColour}
              onChange={setSelectedColour}
            />
          </div>
          <div className="flex items-center gap-3 px-1">
            <label className="text-sm font-medium" htmlFor="strokeWidthSelect">
              Stroke Width:
            </label>
            <select
              id="strokeWidthSelect"
              className="rounded border px-2 py-1 text-sm"
              value={strokeWidth}
              onChange={(e) => setStrokeWidth(Number(e.target.value))}
            >
              {[0.5, 1, 1.5, 2].map((w) => (
                <option key={w} value={w}>
                  {w}px
                </option>
              ))}
            </select>
          </div>
          <div className="px-1">
            <DrawingModePicker
              selected={drawingMode}
              onChange={setDrawingMode}
            />
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
