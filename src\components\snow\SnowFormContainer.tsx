"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useHotkeys } from "react-hotkeys-hook";
import type { z } from "zod";
import { AthleteSelect } from "~/app/validation/components/AthleteSelect";
import { EditableSelector } from "~/components/Selector";
import { Input } from "~/components/ui/input";
import { useUpsertSnowTags } from "~/hooks/snowTags";
import { useStore } from "~/hooks/store";
import {
  SnowEvent,
  SnowFeatureType,
  SnowLandingType,
  SnowLandingZone,
  SnowRailSpinDirection,
} from "~/lib/enums/snow";
import type { TaglistTag } from "~/lib/interface";
import type { SnowRaceResult } from "~/lib/interfaces/externalCall";
import { snowFormSchema } from "~/lib/schemas";
import { cn, getSnowSectionWord } from "~/lib/utils";
import type { GetRaceOutput } from "~/server/api/routers/snow";
import type {
  BigAirTag,
  HalfPipeTag,
  HalfPipeWith,
  NewSnowSlopeJumpTag,
  NewSnowSlopeRailTag,
  NewSnowSlopeTransitionTransitionTag,
  SlopeRailTag,
  TransitionTransitionTag,
} from "~/server/db/snowSchema";
import { api } from "~/trpc/react";
import { Card } from "../Card";
import { HotkeyButton } from "../HotkeyButton";
import { Button } from "../ui/button";
import { Form } from "../ui/form";
import { Label } from "../ui/label";
import { ScrollArea } from "../ui/scroll-area";
import { Skeleton } from "../ui/skeleton";
import { SnowEditRace } from "./EditRace";
import { SectionFeatureSelect } from "./enumSelectors/SectionFeatureSelect";
import { SlopeFeatureSelect } from "./enumSelectors/SlopeFeatureSelect";
import { FormBigAir } from "./forms/FormBigAir";
import { FormHalfPipe } from "./forms/FormHalfpipe";
import { FormSlopeParent } from "./forms/FormSlopeParent";

export interface SnowFormParentStates {
  runScore?: string;
  overallScore?: string;
  featureTypeId?: string;
  sectionNum?: string;
  startFrame: string;
  endFrame: string;
}

export type SnowFormProps = z.infer<typeof snowFormSchema>;

const initialFormData = {
  trickNum: null,
  amplitude: null,
  jumpTypeId: null,
  jumpTakeoffModifierId: null,
  progression: "0",
  switch: "0",
  cab: "0",
  spinDirection: "none",
  spinTypeId: null,
  spinAmount: "0",
  spinModifierId: null,
  grabTypeId: null,
  grabStart: null,
  grabEnd: null,
  takeOffFrame: null,
  landingFrame: null,
  executionId: null,
  landingZone: SnowLandingZone.none,
  landingType: SnowLandingType.none,
  landingDescriptions: [],
  score: null,
  //rail fields
  railFeatureId: null,
  railInSpinDirection: SnowRailSpinDirection.none,
  railInSpinAmount: "0",
  railInSpinModifierId: null,
  railInGrabId: null,
  railTrickId: null,
  railOnSpinDirection: SnowRailSpinDirection.none,
  railOnSpinAmount: "0",
  railOnSpinModifierId: null,
  railOnGrabId: null,
  railOutSpinDirection: SnowRailSpinDirection.none,
  railOutSpinAmount: "0",
  railOutSpinModifierId: null,
  railOutGrabId: null,
};

const getAthleteOptions = (race?: GetRaceOutput) => {
  if (!race) return [];
  return race.results.map((x) => ({
    name: x.athlete.first_name + " " + x.athlete.last_name,
    athleteId: x.athlete_id,
  }));
};

const getRunOptions = (result?: SnowRaceResult) => {
  return (
    result?.runs
      ?.sort((a, b) => a.run - b.run)
      .map((x) => ({
        value: x.run_id,
        label: x.run.toString(),
      })) ?? []
  );
};

export const SnowFormContainer = ({ raceId }: { raceId?: string }) => {
  const utils = api.useUtils();
  const params = useParams();
  const videoId = params.id as string;

  const currentFrame = useStore((state) => state.currentFrame);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const setSelectedAthlete = useStore((state) => state.setSelectedAthlete);
  const setFilteredTags = useStore((state) => state.setFilteredTags);

  const [selectedRunId, setSelectedRunId] = useState<string>(); //run id
  const [parentData, setParentData] = useState<SnowFormParentStates>({
    runScore: "",
    overallScore: "",
    featureTypeId: "",
    sectionNum: "1",
    startFrame: "",
    endFrame: "",
  });

  const form = useForm<SnowFormProps>({
    resolver: zodResolver(snowFormSchema),
    defaultValues: { ...initialFormData },
  });

  const sectionNum = parentData.sectionNum;

  const { data: slopeFeatures } = api.snow.getSlopeFeatures.useQuery(
    {
      raceId: raceId ?? "",
    },
    {
      enabled: !!raceId,
    },
  );
  const { data: race, isLoading: isLoadingRace } = api.snow.getRace.useQuery(
    {
      raceId: raceId ?? "",
    },
    {
      enabled: !!raceId,
    },
  );
  const { data: tag } = api.snow.getTags.useQuery(
    {
      runId: selectedRunId!,
      event: race?.event ?? SnowEvent.HALF_PIPE,
      featureTypeId: parentData.featureTypeId,
      sectionNum: sectionNum ? parseInt(sectionNum) : undefined,
    },
    { enabled: !!selectedRunId && !!race && !!race.event },
  );

  const snowEvent = race?.event;

  const { mutate: upsertRun, isPending: isPendingAddRun } =
    api.snow.upsertRun.useMutation({
      onError: (error) => {
        toast.error(error.message);
      },
      onSuccess: async (data) => {
        await utils.snow.getRace.invalidate({ raceId });
        setSelectedRunId(data.run_id);
      },
    });
  const { mutate: deleteRun } = api.snow.deleteRun.useMutation({
    onError: (error) => {
      toast.error(error.message);
    },
    onSuccess: () => {
      void utils.snow.getRace.invalidate({ raceId });
    },
  });

  const grabStartFrame = form.watch("grabStart");
  const grabEndFrame = form.watch("grabEnd");
  const takeOffFrame = form.watch("takeOffFrame");
  const landingFrame = form.watch("landingFrame");

  useEffect(() => {
    if (!videoId || !selectedAthlete) return;
    const timelineTags: TaglistTag[] = [
      {
        id: "startFrame",
        videoId,
        userId: "mockuser",
        frame: +parentData.startFrame,
        tag: "startFrame",
        athleteId: selectedAthlete,
        isDeleted: false,
      },
      {
        id: "endFrame",
        videoId,
        userId: "mockuser",
        frame: +parentData.endFrame,
        tag: "endFrame",
        athleteId: selectedAthlete,
        isDeleted: false,
      },
      {
        id: "grabStart",
        videoId,
        userId: "mockuser",
        frame: +(grabStartFrame ?? 0),
        tag: "grabStart",
        athleteId: selectedAthlete,
        isDeleted: false,
      },
      {
        id: "grabEnd",
        videoId,
        userId: "mockuser",
        frame: +(grabEndFrame ?? 0),
        tag: "grabEnd",
        athleteId: selectedAthlete,
        isDeleted: false,
      },
      {
        id: "takeOffFrame",
        videoId,
        userId: "mockuser",
        frame: +(takeOffFrame ?? 0),
        tag: "takeOffFrame",
        athleteId: selectedAthlete,
        isDeleted: false,
      },
      {
        id: "landingFrame",
        videoId,
        userId: "mockuser",
        frame: +(landingFrame ?? 0),
        tag: "landingFrame",
        athleteId: selectedAthlete,
        isDeleted: false,
      },
    ];
    setFilteredTags(timelineTags);

    return () => {
      setFilteredTags([]);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    parentData,
    selectedAthlete,
    videoId,
    grabStartFrame,
    grabEndFrame,
    takeOffFrame,
    landingFrame,
  ]);

  const {
    onUpsertHalfpipeTag,
    isPendingUpsertHalfpipeTag,
    onUpsertBigAirTag,
    isPendingUpsertBigAirTag,
    onUpsertSlopeStyleTag,
    isPendingUpsertSlopeStyleTag,
  } = useUpsertSnowTags({
    event: snowEvent,
    parentData,
    raceId,
    runId: selectedRunId,
  });

  const result = race?.results.find((x) => x.athlete_id === selectedAthlete);
  const selectedRun = result?.runs.find((x) => x.run_id === selectedRunId);

  const runOptions = getRunOptions(result);
  const athleteOptions = getAthleteOptions(race);

  const selectedSlopeFeature = slopeFeatures?.find(
    (x) => x.id === parentData.featureTypeId,
  );

  useEffect(() => {
    //when race loaded, set first athlete as selected
    setSelectedAthlete(athleteOptions[0]?.athleteId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [race]);

  useEffect(() => {
    // when selected athlete change, update run options
    if (runOptions?.[0]) {
      setSelectedRunId(runOptions[0].value);
    } else {
      setSelectedRunId(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAthlete]);

  useEffect(() => {
    // when selected run change, update parent data
    const newParentData = { ...parentData };

    const typedTag = tag as HalfPipeWith;
    const slopeRailTag = tag as SlopeRailTag;
    const halfPipeTag = tag as HalfPipeTag;
    const bigAirTag = tag as BigAirTag;
    const transitionTransitionTag = tag as TransitionTransitionTag;

    newParentData.runScore = selectedRun?.score?.toString() ?? "";
    newParentData.overallScore = selectedRun?.overall_score?.toString() ?? "";
    newParentData.startFrame = halfPipeTag?.startFrame.toString() ?? "";
    newParentData.endFrame = halfPipeTag?.endFrame.toString() ?? "";
    setParentData(newParentData);

    //when tag change, update form data
    if (!tag) {
      form.reset();
    } else {
      switch (snowEvent) {
        case SnowEvent.HALF_PIPE:
          // halfpipe fields
          form.setValue("trickNum", halfPipeTag.trickNum?.toString() ?? null);
          form.setValue("amplitude", halfPipeTag.amplitude?.toString() ?? null);
          form.setValue(
            "jumpTypeId",
            halfPipeTag.jumpTypeId.toString() ?? null,
          );
          break;
        case SnowEvent.BIG_AIR:
          // big air fields
          form.setValue("jumpTypeId", bigAirTag.jumpTypeId.toString() ?? null);
          form.setValue("score", bigAirTag.score?.toString() ?? null);
          break;
        case SnowEvent.SLOPESTYLE:
          // slopestyle fields
          form.setValue("jumpTypeId", bigAirTag.jumpTypeId?.toString() ?? null);
          form.setValue("score", bigAirTag.score?.toString() ?? null);
          //If it is rail
          if (slopeRailTag.railFeatureId) {
            form.setValue(
              "railFeatureId",
              slopeRailTag.railFeatureId.toString(),
            );
            form.setValue(
              "railInSpinDirection",
              slopeRailTag.railInSpinDirection ?? SnowRailSpinDirection.none,
            );
            form.setValue(
              "railInSpinAmount",
              slopeRailTag.railInSpinAmount?.toString() ?? null,
            );
            form.setValue(
              "railInSpinModifierId",
              slopeRailTag.railInSpinModifierId?.toString() ?? null,
            );
            form.setValue(
              "railInGrabId",
              slopeRailTag.railInGrabId?.toString() ?? null,
            );
            form.setValue(
              "railTrickId",
              slopeRailTag.railTrickId?.toString() ?? null,
            );
            form.setValue(
              "railOnSpinDirection",
              slopeRailTag.railOnSpinDirection ?? SnowRailSpinDirection.none,
            );
            form.setValue(
              "railOnSpinAmount",
              slopeRailTag.railOnSpinAmount?.toString() ?? null,
            );
            form.setValue(
              "railOnSpinModifierId",
              slopeRailTag.railOnSpinModifierId?.toString() ?? null,
            );
            form.setValue(
              "railOnGrabId",
              slopeRailTag.railOnGrabId?.toString() ?? null,
            );
            form.setValue(
              "railOutSpinDirection",
              slopeRailTag.railOutSpinDirection ?? SnowRailSpinDirection.none,
            );
            form.setValue(
              "railOutSpinAmount",
              slopeRailTag.railOutSpinAmount?.toString() ?? null,
            );
            form.setValue(
              "railOutSpinModifierId",
              slopeRailTag.railOutSpinModifierId?.toString() ?? null,
            );
            form.setValue(
              "railOutGrabId",
              slopeRailTag.railOutGrabId?.toString() ?? null,
            );
          }
          if (transitionTransitionTag.transitionTypeId) {
            form.setValue(
              "transitionTypeId",
              transitionTransitionTag.transitionTypeId?.toString() ?? null,
            );
          }
          break;
        default:
          break;
      }
      // common fields
      form.setValue("id", tag.id);
      form.setValue(
        "jumpTakeoffModifierId",
        typedTag.jumpTakeoffModifierId?.toString() ?? null,
      );
      form.setValue("progression", typedTag.progression ? "1" : "0");
      form.setValue("switch", typedTag.switch ? "1" : "0");
      form.setValue("cab", typedTag.cab ? "1" : "0");
      form.setValue("spinDirection", typedTag?.spinDirection ?? "none");
      form.setValue("spinTypeId", typedTag.spinTypeId?.toString() ?? null);
      form.setValue("spinAmount", typedTag.spinAmount?.toString() ?? null);
      form.setValue(
        "spinModifierId",
        typedTag.spinModifierId?.toString() ?? null,
      );
      form.setValue("grabTypeId", typedTag.grabTypeId?.toString() ?? null);
      form.setValue("grabStart", typedTag.grabStart?.toString() ?? null);
      form.setValue("grabEnd", typedTag.grabEnd?.toString() ?? null);
      form.setValue("takeOffFrame", typedTag.takeOffFrame?.toString() ?? null);
      form.setValue("landingFrame", typedTag.landingFrame?.toString() ?? null);
      form.setValue("executionId", typedTag.executionId?.toString() ?? null);
      form.setValue("landingZone", typedTag.landingZone?.toString() ?? null);
      form.setValue("landingType", typedTag.landingType?.toString() ?? null);
      form.setValue(
        "landingDescriptions",
        typedTag.landingDescriptions.map((x) => x.value) ?? [],
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRunId, tag]);

  const onAddRun = (value: string) => {
    if (!result) return;
    const number = parseInt(value);
    if (isNaN(number)) {
      toast.error("Invalid run number");
      return;
    }
    upsertRun({
      resultId: result.result_id,
      run: number,
    });
  };

  const onRemoveRun = (value: string) => {
    deleteRun({ id: value });
  };

  const onSetStartFrame = () => {
    setParentData((prev) => ({
      ...prev,
      startFrame: currentFrame.toString(),
    }));
  };

  const onSetEndFrame = () => {
    setParentData((prev) => ({
      ...prev,
      endFrame: currentFrame.toString(),
    }));
  };

  const onUpsertTag = () => {
    const formValues = form.getValues();
    if (!result || !selectedRunId || !selectedRun?.run || !snowEvent) return;

    const { startFrame, endFrame } = parentData;
    // Check if startFrame or endFrame are empty
    if (!startFrame || !endFrame) {
      toast.error("Start frame and end frame must be set");
      return;
    }

    if (+startFrame < 1 || +endFrame < 1 || +startFrame > +endFrame) {
      toast.error("Invalid frame range");
      return;
    }
    switch (snowEvent) {
      case SnowEvent.HALF_PIPE:
        onUpsertHalfpipeTag({
          formValues,
          resultId: result.result_id,
          run: selectedRun.run,
        });
        break;
      case SnowEvent.BIG_AIR:
        onUpsertBigAirTag({
          formValues,
          resultId: result.result_id,
          run: selectedRun.run,
        });
        break;
      case SnowEvent.SLOPESTYLE:
        let sectionType: SnowFeatureType = SnowFeatureType.jump;
        if (selectedSlopeFeature?.type === SnowFeatureType.rail) {
          sectionType = SnowFeatureType.rail;
        } else if (selectedSlopeFeature?.type === SnowFeatureType.transition) {
          sectionType =
            getSnowSectionWord(
              +(parentData.sectionNum ?? "1"),
              selectedSlopeFeature,
            ) ?? SnowFeatureType.jump;
        }
        onUpsertSlopeStyleTag({
          formValues,
          resultId: result.result_id,
          run: selectedRun.run,
          sectionType,
        });
        break;
      default:
        break;
    }
  };

  useHotkeys("1", onSetStartFrame);
  useHotkeys("0", onSetEndFrame);

  return (
    <div className="flex max-h-[calc(100vh-200px)] flex-col gap-2">
      <SnowEditRace race={race} />
      {isLoadingRace && <Skeleton className="h-40 w-full min-w-40" />}
      {race && (
        <>
          <AthleteSelect athleteOptions={athleteOptions} />
          <ScrollArea className="flex-1">
            <div className="flex flex-col gap-2.5 pb-2">
              <Card
                className={cn(
                  "gap-x-[5px] gap-y-2.5",
                  snowEvent === SnowEvent.SLOPESTYLE
                    ? "flex flex-col"
                    : "grid grid-cols-2",
                )}
              >
                <div className="col-span-2">
                  <Label className="!text-smallLabel">RUN DETAILS:</Label>
                </div>
                <div
                  className={cn(
                    snowEvent === SnowEvent.SLOPESTYLE ? "" : "col-span-2",
                  )}
                >
                  <EditableSelector
                    label="Number:"
                    options={runOptions}
                    onAdd={onAddRun}
                    onRemove={onRemoveRun}
                    type="number"
                    easyAdd
                    value={selectedRunId}
                    onSelect={(value) => {
                      setSelectedRunId(value);
                    }}
                    isPendingAdd={isPendingAddRun}
                  />
                </div>
                {snowEvent &&
                  [SnowEvent.HALF_PIPE, SnowEvent.SLOPESTYLE].includes(
                    snowEvent,
                  ) && (
                    <>
                      {snowEvent === SnowEvent.SLOPESTYLE ? (
                        <div className="grid grid-cols-2 gap-[5px]">
                          <Input
                            label="Score"
                            value={parentData.runScore}
                            containerClassName="flex-col gap-[5px] items-start"
                            className="w-full"
                            type="number"
                            onChange={(e) => {
                              setParentData((prev) => ({
                                ...prev,
                                runScore: e.target.value,
                              }));
                            }}
                          />
                          <Input
                            label="Overall Impression"
                            value={parentData.overallScore?.toString()}
                            containerClassName="flex-col gap-[5px] items-start"
                            className="w-full"
                            type="number"
                            onChange={(e) => {
                              setParentData((prev) => ({
                                ...prev,
                                overallScore: e.target.value,
                              }));
                            }}
                          />
                        </div>
                      ) : (
                        <Input
                          label="Run score"
                          value={parentData.runScore}
                          containerClassName="flex-col gap-[5px] items-start"
                          className="w-full"
                          type="number"
                          onChange={(e) => {
                            setParentData((prev) => ({
                              ...prev,
                              runScore: e.target.value,
                            }));
                          }}
                        />
                      )}
                    </>
                  )}
                {snowEvent === SnowEvent.SLOPESTYLE && (
                  <>
                    <SlopeFeatureSelect
                      raceId={race.event_id}
                      value={parentData.featureTypeId ?? ""}
                      onChange={(value) => {
                        const feature = slopeFeatures?.find(
                          (x) => x.id === value,
                        );
                        if (!feature) return;
                        const isJump = feature.type === SnowFeatureType.jump;
                        setParentData((prev) => ({
                          ...prev,
                          featureTypeId: value,
                          sectionNum: isJump ? "1" : prev.sectionNum,
                        }));
                      }}
                    />
                    {selectedSlopeFeature &&
                      selectedSlopeFeature?.type !== SnowFeatureType.jump && (
                        <SectionFeatureSelect
                          value={parentData.sectionNum}
                          feature={selectedSlopeFeature}
                          onChange={(value) => {
                            setParentData((prev) => ({
                              ...prev,
                              sectionNum: value,
                            }));
                          }}
                        />
                      )}
                  </>
                )}
                <div className="col-span-2 grid grid-cols-2 gap-[5px]">
                  <Label className="!text-smallLabel after:ml-0.5 after:font-black after:text-red-500 after:content-['*']">
                    Start Frame:
                  </Label>
                  <div className="flex w-full items-center gap-[5px]">
                    <Input
                      containerClassName="flex-1"
                      className="w-12 flex-1"
                      value={parentData.startFrame}
                      type="number"
                      onChange={(e) => {
                        setParentData((prev) => ({
                          ...prev,
                          startFrame: e.target.value,
                        }));
                      }}
                    />
                    <HotkeyButton
                      onClick={onSetStartFrame}
                      tag={{
                        value: "1",
                        key: "1",
                        className: "h-5 w-5 text-label",
                      }}
                    />
                  </div>
                </div>
              </Card>

              {selectedRunId && (
                <>
                  <Form {...form}>
                    <form className="flex flex-col gap-2.5">
                      {snowEvent === SnowEvent.HALF_PIPE && (
                        <FormHalfPipe
                          form={form}
                          isSnowboard={race.discipline === "SNOWBOARD"}
                          tag={tag as HalfPipeTag}
                        />
                      )}
                      {snowEvent === SnowEvent.BIG_AIR && (
                        <FormBigAir
                          form={form}
                          isSnowboard={race.discipline === "SNOWBOARD"}
                        />
                      )}
                      {snowEvent === SnowEvent.SLOPESTYLE && (
                        <FormSlopeParent
                          form={form}
                          isSnowboard={race.discipline === "SNOWBOARD"}
                          feature={selectedSlopeFeature}
                          sectionNum={+(parentData.sectionNum ?? "1")}
                          jumpTag={tag as NewSnowSlopeJumpTag}
                          railTag={tag as NewSnowSlopeRailTag}
                          transitionTag={
                            tag as NewSnowSlopeTransitionTransitionTag
                          }
                          setParentData={setParentData}
                        />
                      )}
                    </form>
                  </Form>
                  <Card>
                    <div className="col-span-2 grid grid-cols-2 gap-[5px]">
                      <Label className="!text-smallLabel after:ml-0.5 after:font-black after:text-red-500 after:content-['*']">
                        End Frame:
                      </Label>
                      <div className="flex w-full items-center">
                        <Input
                          containerClassName="flex-1"
                          className="w-12 flex-1"
                          value={parentData.endFrame}
                          type="number"
                          onChange={(e) => {
                            setParentData((prev) => ({
                              ...prev,
                              endFrame: e.target.value,
                            }));
                          }}
                        />
                        <HotkeyButton
                          onClick={onSetEndFrame}
                          tag={{
                            value: "0",
                            key: "0",
                            className: "h-5 w-5 text-label",
                          }}
                        />
                      </div>
                    </div>
                  </Card>
                </>
              )}
            </div>
          </ScrollArea>

          {selectedRunId && (
            <Button
              loading={
                isPendingUpsertHalfpipeTag ||
                isPendingUpsertBigAirTag ||
                isPendingUpsertSlopeStyleTag
              }
              onClick={onUpsertTag}
            >
              Save Tag
            </Button>
          )}
        </>
      )}
    </div>
  );
};
